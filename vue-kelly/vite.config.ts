/*
 * @Author: <NAME_EMAIL>
 * @Date: 2025-04-19 12:19:59
 * @LastEditors: <NAME_EMAIL>
 * @LastEditTime: 2025-08-03 22:19:44
 * @FilePath: /projects/vue-kelly/vite.config.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import { fileURLToPath, URL } from 'node:url'

import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import vueJsx from '@vitejs/plugin-vue-jsx'
import UnoCSS from 'unocss/vite'
import { viteMockServe } from 'vite-plugin-mock'
// import vueDevTools from 'vite-plugin-vue-devtools' // 注释掉这行

// https://vite.dev/config/
export default defineConfig({
  plugins: [
    vue(),
    vueJsx(),
    UnoCSS(),
    viteMockServe({
      mockPath: 'mock',
      enable: true,
    }),
    // vueDevTools(), // 注释掉这行
  ],
  css: {
    preprocessorOptions: {
      less: {
        javascriptEnabled: true, // 必须启用
        modifyVars: {
          // 在这里自定义 Ant Design Vue 的主题变量
          'primary-color': '#1DA57A', // 主色
          'link-color': '#1DA57A', // 链接色
          'border-radius-base': '4px', // 圆角
        },
      },
    },
  },
  resolve: {
    alias: {
      '@': fileURLToPath(new URL('./src', import.meta.url)),
    },
  },
})
