/*
 * @Author: <NAME_EMAIL>
 * @Date: 2025-08-03 23:10:00
 * @LastEditors: <NAME_EMAIL>
 * @LastEditTime: 2025-08-03 23:17:15
 * @FilePath: /vue-kelly/mock/auth.ts
 * @Description: 认证相关 Mock API
 */
import { MockMethod } from 'vite-plugin-mock'

// Mock 用户数据
const mockUsers = [
  {
    id: 1,
    username: 'admin',
    password: 'admin123',
    name: '管理员',
    role: 'admin',
    avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=admin',
    email: '<EMAIL>',
    phone: '13800138000',
    status: 'active',
    createTime: '2024-01-01 00:00:00',
    lastLoginTime: '2025-08-03 23:00:00',
  },
  {
    id: 2,
    username: 'user',
    password: 'user123',
    name: '普通用户',
    role: 'user',
    avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=user',
    email: '<EMAIL>',
    phone: '13800138001',
    status: 'active',
    createTime: '2024-01-02 00:00:00',
    lastLoginTime: '2025-08-03 22:30:00',
  },
  {
    id: 3,
    username: 'guest',
    password: 'guest123',
    name: '访客用户',
    role: 'guest',
    avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=guest',
    email: '<EMAIL>',
    phone: '13800138002',
    status: 'active',
    createTime: '2024-01-03 00:00:00',
    lastLoginTime: '2025-08-03 22:00:00',
  },
]

// 生成随机 token
const generateToken = (username: string) => {
  const timestamp = Date.now()
  const random = Math.random().toString(36).substr(2, 9)
  return `${username}_${timestamp}_${random}`
}

// 验证 token
const validateToken = (token: string) => {
  if (!token || !token.includes('_')) {
    return null
  }

  const [username] = token.split('_')
  return mockUsers.find((user) => user.username === username)
}

export default [
  // 登录接口
  {
    url: '/api/auth/login',
    method: 'post',
    response: ({ body }) => {
      const { username, password } = body

      // 模拟网络延迟
      const delay = Math.random() * 1000 + 500

      return new Promise((resolve) => {
        setTimeout(() => {
          const user = mockUsers.find((u) => u.username === username && u.password === password)

          if (user) {
            const token = generateToken(username)
            // eslint-disable-next-line @typescript-eslint/no-unused-vars
            const { password: _password, ...userInfo } = {
              ...user,
              token,
              lastLoginTime: new Date().toISOString(),
            }

            resolve({
              code: 200,
              success: true,
              data: userInfo,
              message: '登录成功',
            })
          } else {
            const errorMessages = ['账号或密码错误', '用户不存在', '密码错误，请重试']

            resolve({
              code: 401,
              success: false,
              data: null,
              message: errorMessages[Math.floor(Math.random() * errorMessages.length)],
            })
          }
        }, delay)
      })
    },
  },

  // 登出接口
  {
    url: '/api/auth/logout',
    method: 'post',
    response: ({ headers }) => {
      const token = headers.authorization?.replace('Bearer ', '')

      return new Promise((resolve) => {
        setTimeout(() => {
          if (validateToken(token)) {
            resolve({
              code: 200,
              success: true,
              data: null,
              message: '登出成功',
            })
          } else {
            resolve({
              code: 401,
              success: false,
              data: null,
              message: 'Token 无效',
            })
          }
        }, 300)
      })
    },
  },

  // 获取用户信息接口
  {
    url: '/api/auth/userinfo',
    method: 'get',
    response: ({ headers }) => {
      const token = headers.authorization?.replace('Bearer ', '')

      return new Promise((resolve) => {
        setTimeout(() => {
          const user = validateToken(token)

          if (user) {
            // eslint-disable-next-line @typescript-eslint/no-unused-vars
            const { password: _password, ...userInfo } = { ...user, token }

            resolve({
              code: 200,
              success: true,
              data: userInfo,
              message: '获取用户信息成功',
            })
          } else {
            resolve({
              code: 401,
              success: false,
              data: null,
              message: 'Token 无效或已过期',
            })
          }
        }, 200)
      })
    },
  },

  // 获取测试账号列表
  {
    url: '/api/auth/test-accounts',
    method: 'get',
    response: () => {
      return {
        code: 200,
        success: true,
        data: mockUsers.map((user) => ({
          username: user.username,
          password: user.password,
          name: user.name,
          role: user.role,
        })),
        message: '获取测试账号成功',
      }
    },
  },
] as MockMethod[]
