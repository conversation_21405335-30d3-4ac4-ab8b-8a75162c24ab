# UnoCSS 使用指南

## 简介

UnoCSS 已成功集成到项目中，提供了原子化 CSS 的强大功能。

## 配置文件

- `uno.config.ts` - UnoCSS 主配置文件
- `src/main.ts` - 已导入 `virtual:uno.css`
- `env.d.ts` - 已添加类型定义

## 预设功能

### 1. 原子化 CSS 类
使用 Tailwind CSS 兼容的原子化类：
```html
<div class="p-4 bg-blue-500 text-white rounded-lg">
  原子化样式
</div>
```

### 2. 自定义快捷方式

#### 按钮样式
- `btn` - 基础按钮样式
- `btn-primary` - 主要按钮（蓝色）
- `btn-secondary` - 次要按钮（灰色）
- `btn-success` - 成功按钮（绿色）
- `btn-danger` - 危险按钮（红色）
- `btn-warning` - 警告按钮（黄色）

```html
<button class="btn-primary">主要按钮</button>
<button class="btn-danger">删除</button>
```

#### 卡片样式
- `card` - 基础卡片样式
- `card-header` - 卡片头部
- `card-body` - 卡片内容

```html
<div class="card">
  <div class="card-header">
    <h3>标题</h3>
  </div>
  <div class="card-body">
    内容
  </div>
</div>
```

#### 布局快捷方式
- `flex-center` - 居中对齐
- `flex-between` - 两端对齐
- `input` - 输入框样式

### 3. 自定义主题色
配置了 `primary` 颜色系列（蓝色系），可以使用：
- `text-primary-500`
- `bg-primary-600`
- `border-primary-300`
等等

### 4. 属性化模式
可以使用属性化语法：
```html
<div bg="blue-500" text="white" p="4" rounded>
  属性化语法
</div>
```

### 5. 指令支持
支持 CSS 指令：
```css
.custom-class {
  @apply p-4 bg-blue-500 text-white rounded;
}
```

## 开发工具

### UnoCSS Inspector
开发服务器运行时，访问 `http://localhost:5174/__unocss/` 查看：
- 生成的 CSS
- 使用的类
- 配置信息

## 最佳实践

1. **优先使用原子化类**：对于简单样式，直接使用原子化类
2. **创建快捷方式**：对于重复的样式组合，在 `uno.config.ts` 中定义快捷方式
3. **使用语义化命名**：快捷方式使用语义化的名称，如 `btn-primary` 而不是 `btn-blue`
4. **合理使用属性化模式**：在模板中样式较多时使用属性化模式提高可读性

## 与 Ant Design Vue 的配合

UnoCSS 与 Ant Design Vue 可以很好地配合使用：
- UnoCSS 用于布局和自定义样式
- Ant Design Vue 提供组件功能
- 两者样式不会冲突

## 扩展配置

如需添加更多功能，可以在 `uno.config.ts` 中：
- 添加更多预设
- 定义更多快捷方式
- 配置自定义主题
- 添加图标集合
