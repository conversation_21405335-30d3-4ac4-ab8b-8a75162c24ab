# 用户登录功能使用指南

## 功能概述

本项目实现了完整的用户登录功能，使用 `vite-plugin-mock` 模拟后端 API 接口。

## 技术栈

- **前端框架**: Vue 3 + TypeScript
- **UI 组件库**: Ant Design Vue
- **状态管理**: Pinia
- **样式框架**: UnoCSS
- **Mock 工具**: vite-plugin-mock + mockjs

## 功能特性

### 1. 登录表单
- 账号和密码输入框
- 表单验证（必填、长度限制）
- 记住我选项
- 加载状态显示

### 2. Mock API
- 模拟真实的网络请求
- 随机延迟（500-1500ms）
- 多种错误情况模拟
- JWT Token 模拟

### 3. 状态管理
- 用户信息持久化存储
- 登录状态管理
- 角色权限管理

### 4. 用户体验
- 点击测试账号快速填入
- 实时表单验证
- 友好的错误提示
- 响应式设计

## 测试账号

| 角色 | 账号 | 密码 | 说明 |
|------|------|------|------|
| 管理员 | admin | admin123 | 具有管理员权限 |
| 普通用户 | user | user123 | 普通用户权限 |
| 访客用户 | guest | guest123 | 访客权限 |

## 文件结构

```
src/
├── views/UsersView/
│   └── index.vue              # 登录页面组件
├── stores/
│   └── user.ts                # 用户状态管理
├── api/
│   └── auth.ts                # 认证相关 API
├── utils/
│   └── request.ts             # HTTP 请求工具
└── test-mock.ts               # Mock API 测试

mock/
├── auth.ts                    # 认证相关 Mock 配置
└── index.ts                   # Mock 入口文件
```

## API 接口

### 1. 登录接口
```typescript
POST /api/auth/login
{
  "username": "admin",
  "password": "admin123",
  "remember": true
}
```

### 2. 登出接口
```typescript
POST /api/auth/logout
Headers: {
  "Authorization": "Bearer <token>"
}
```

### 3. 获取用户信息
```typescript
GET /api/auth/userinfo
Headers: {
  "Authorization": "Bearer <token>"
}
```

### 4. 获取测试账号
```typescript
GET /api/auth/test-accounts
```

## 使用方法

### 1. 启动开发服务器
```bash
npm run dev
# 或
pnpm dev
```

### 2. 访问登录页面
打开浏览器访问 `http://localhost:5173/users`

### 3. 测试登录
- 点击页面下方的测试账号快速填入
- 或手动输入账号密码
- 点击登录按钮

### 4. 查看登录状态
登录成功后会显示用户信息和欢迎消息

## 开发说明

### 1. 添加新的 Mock API
在 `mock/auth.ts` 中添加新的接口配置：

```typescript
{
  url: '/api/your-endpoint',
  method: 'post',
  response: ({ body }) => {
    // 处理逻辑
    return {
      code: 200,
      success: true,
      data: {},
      message: '成功'
    }
  }
}
```

### 2. 修改用户数据
在 `mock/auth.ts` 的 `mockUsers` 数组中修改用户信息

### 3. 自定义验证规则
在 `src/views/UsersView/index.vue` 的 `rules` 对象中修改验证规则

### 4. 扩展用户状态
在 `src/stores/user.ts` 中添加新的状态和方法

## 注意事项

1. **开发环境**: Mock 功能仅在开发环境下生效
2. **生产环境**: 需要替换为真实的后端 API
3. **Token 存储**: 当前使用 localStorage，生产环境建议使用更安全的方式
4. **错误处理**: 已实现基本的错误处理，可根据需要扩展

## 故障排除

### 1. Mock API 不工作
- 检查 `vite.config.ts` 中的 mock 插件配置
- 确认 `mock` 目录下的文件是否正确

### 2. 登录失败
- 检查账号密码是否正确
- 查看浏览器控制台的错误信息
- 确认网络请求是否正常

### 3. 状态不持久化
- 检查浏览器的 localStorage
- 确认用户状态管理是否正确初始化

## 扩展建议

1. **添加验证码**: 增强安全性
2. **忘记密码**: 实现密码重置功能
3. **多因子认证**: 支持短信/邮箱验证
4. **社交登录**: 支持第三方登录
5. **登录历史**: 记录登录日志
