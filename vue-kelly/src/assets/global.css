/* 全局样式重置和修复 */

/* 确保html和body的基础样式 */
html,
body {
  margin: 0;
  padding: 0;
  width: 100%;
  height: 100%;
  overflow-x: hidden;
}

/* 确保根元素样式 */
#app {
  margin: 0 !important;
  padding: 0 !important;
  max-width: none !important;
  width: 100%;
  min-height: 100vh;
  display: block !important;
  grid-template-columns: none !important;
}

/* 修复可能的布局问题 */
* {
  box-sizing: border-box;
}

/* 确保Ant Design Layout组件正常工作 */
.ant-layout {
  min-height: 100vh;
}

.ant-layout-sider {
  position: relative !important;
}

.ant-layout-content {
  flex: 1;
  padding: 0;
  margin-bottom: 2px;
  min-height: 0;
}

/* 防止开发者工具影响布局的样式 */
@media screen {
  body {
    display: block !important;
    place-items: unset !important;
  }

  #app {
    display: block !important;
    grid-template-columns: none !important;
  }
}

/* 确保在所有屏幕尺寸下都正常显示 */
@media (min-width: 1024px) {
  body {
    display: block !important;
    place-items: unset !important;
  }

  #app {
    display: block !important;
    grid-template-columns: none !important;
    padding: 0 !important;
  }
}
