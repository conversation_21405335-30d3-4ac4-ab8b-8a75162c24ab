@import './base.css';

/* 重置默认样式，避免与Ant Design冲突 */
#app {
  margin: 0;
  padding: 0;
  max-width: none;
  font-weight: normal;
  min-height: 100vh;
}

/* 确保body样式不会影响布局 */
body {
  margin: 0;
  padding: 0;
  min-height: 100vh;
}

a,
.green {
  text-decoration: none;
  color: hsla(160, 100%, 37%, 1);
  transition: 0.4s;
  padding: 3px;
}

@media (hover: hover) {
  a:hover {
    background-color: hsla(160, 100%, 37%, 0.2);
  }
}

/* 移除可能导致布局问题的响应式样式 */
/* @media (min-width: 1024px) {
  body {
    display: flex;
    place-items: center;
  }

  #app {
    display: grid;
    grid-template-columns: 1fr 1fr;
    padding: 0 2rem;
  }
} */
