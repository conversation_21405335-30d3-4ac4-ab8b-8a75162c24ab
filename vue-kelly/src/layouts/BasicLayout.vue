<template>
  <a-layout style="min-height: 100vh">
    <a-layout-sider v-model:collapsed="collapsed" collapsible>
      <a-menu
        v-model:selectedKeys="selectedKeys"
        mode="inline"
        theme="dark"
        @click="handleMenuClick"
      >
        <a-menu-item key="home">
          <home-outlined />
          <span>首页</span>
        </a-menu-item>
        <a-menu-item key="users">
          <UserOutlined />
          <span>用户管理</span>
        </a-menu-item>
        <a-menu-item key="about">
          <info-circle-outlined />
          <span>关于</span>
        </a-menu-item>
      </a-menu>
    </a-layout-sider>

    <a-layout>
      <a-layout-header style="background: #fff; padding: 0">
        <div class="header-content">
          <h2>Vue Kelly Admin</h2>
        </div>
      </a-layout-header>

      <a-layout-content style="margin: 16px">
        <div class="content-wrapper">
          <RouterView />
        </div>
      </a-layout-content>

      <a-layout-footer style="text-align: center">
        Vue <PERSON> ©2025 Created by <PERSON> Ma
      </a-layout-footer>
    </a-layout>
  </a-layout>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { HomeOutlined, InfoCircleOutlined, UserOutlined } from '@ant-design/icons-vue'

const router = useRouter()
const route = useRoute()

const collapsed = ref(false)
const selectedKeys = ref<string[]>(['home'])

// 路由映射
const routeMap: Record<string, string> = {
  home: '/',
  about: '/about',
  users: '/users',
}

// 监听路由变化更新选中状态
watch(
  () => route.path,
  (newPath) => {
    const key = Object.keys(routeMap).find((k) => routeMap[k] === newPath)
    if (key) {
      selectedKeys.value = [key]
    }
  },
  { immediate: true },
)

const handleMenuClick = ({ key }: { key: string }) => {
  const path = routeMap[key]
  if (path) {
    router.push(path)
  }
}
</script>

<style scoped>
.logo {
  height: 32px;
  margin: 16px;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 6px;
}

.header-content {
  padding: 0 24px;
  display: flex;
  align-items: center;
  height: 64px;
}

.header-content h2 {
  margin: 0;
  color: #001529;
}

.content-wrapper {
  padding: 24px;
  background: #fff;
  min-height: 680px;
  border-radius: 6px;
}
</style>
