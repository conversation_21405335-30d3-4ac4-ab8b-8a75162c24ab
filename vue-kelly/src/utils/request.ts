/*
 * @Author: <NAME_EMAIL>
 * @Date: 2025-08-03 23:15:00
 * @LastEditors: <NAME_EMAIL>
 * @LastEditTime: 2025-08-03 23:12:38
 * @FilePath: /vue-kelly/src/utils/request.ts
 * @Description: HTTP 请求工具
 */

// API 响应接口
export interface ApiResponse<T = any> {
  code: number
  success: boolean
  data: T
  message: string
}

// 请求配置接口
export interface RequestConfig {
  method?: 'GET' | 'POST' | 'PUT' | 'DELETE'
  headers?: Record<string, string>
  body?: any
}

// 获取存储的 token
const getToken = (): string | null => {
  const userInfo = localStorage.getItem('userInfo')
  if (userInfo) {
    try {
      const user = JSON.parse(userInfo)
      return user.token
    } catch {
      return null
    }
  }
  return null
}

// HTTP 请求函数
export const request = async <T = any>(
  url: string,
  config: RequestConfig = {},
): Promise<ApiResponse<T>> => {
  const { method = 'GET', headers = {}, body } = config

  // 设置默认请求头
  const defaultHeaders: Record<string, string> = {
    'Content-Type': 'application/json',
    ...headers,
  }

  // 添加认证头
  const token = getToken()
  if (token) {
    defaultHeaders.Authorization = `Bearer ${token}`
  }

  // 构建请求配置
  const fetchConfig: RequestInit = {
    method,
    headers: defaultHeaders,
  }

  // 添加请求体
  if (body && method !== 'GET') {
    fetchConfig.body = JSON.stringify(body)
  }

  try {
    const response = await fetch(url, fetchConfig)

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }

    const result = await response.json()
    return result
  } catch (error) {
    console.error('Request failed:', error)

    // 返回错误响应格式
    return {
      code: 500,
      success: false,
      data: null as T,
      message: error instanceof Error ? error.message : '网络请求失败',
    }
  }
}

// GET 请求
export const get = <T = any>(url: string, headers?: Record<string, string>) => {
  return request<T>(url, { method: 'GET', headers })
}

// POST 请求
export const post = <T = any>(url: string, data?: any, headers?: Record<string, string>) => {
  return request<T>(url, { method: 'POST', body: data, headers })
}

// PUT 请求
export const put = <T = any>(url: string, data?: any, headers?: Record<string, string>) => {
  return request<T>(url, { method: 'PUT', body: data, headers })
}

// DELETE 请求
export const del = <T = any>(url: string, headers?: Record<string, string>) => {
  return request<T>(url, { method: 'DELETE', headers })
}
