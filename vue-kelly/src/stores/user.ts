/*
 * @Author: <NAME_EMAIL>
 * @Date: 2025-08-03 23:01:00
 * @LastEditors: <NAME_EMAIL>
 * @LastEditTime: 2025-08-03 23:01:00
 * @FilePath: /vue-kelly/src/stores/user.ts
 * @Description: 用户状态管理
 */
import { ref, computed } from 'vue'
import { defineStore } from 'pinia'

// 用户信息接口
export interface UserInfo {
  id: number
  username: string
  name: string
  role: string
  token: string
  avatar?: string
}

// 登录响应接口
export interface LoginResponse {
  success: boolean
  data: UserInfo | null
  message: string
}

export const useUserStore = defineStore('user', () => {
  // 用户信息
  const userInfo = ref<UserInfo | null>(null)

  // 是否已登录
  const isLoggedIn = computed(() => !!userInfo.value)

  // 用户角色
  const userRole = computed(() => userInfo.value?.role || '')

  // 是否是管理员
  const isAdmin = computed(() => userRole.value === 'admin')

  // 设置用户信息
  const setUser = (user: UserInfo | null) => {
    userInfo.value = user
    if (user) {
      // 保存到 localStorage
      localStorage.setItem('userInfo', JSON.stringify(user))
    } else {
      // 清除 localStorage
      localStorage.removeItem('userInfo')
    }
  }

  // 登出
  const logout = () => {
    setUser(null)
    localStorage.removeItem('rememberedUser')
  }

  // 从 localStorage 恢复用户信息
  const restoreUser = () => {
    const savedUser = localStorage.getItem('userInfo')
    if (savedUser) {
      try {
        const user = JSON.parse(savedUser) as UserInfo
        userInfo.value = user
      } catch (error) {
        console.error('恢复用户信息失败:', error)
        localStorage.removeItem('userInfo')
      }
    }
  }

  // 更新用户信息
  const updateUser = (updates: Partial<UserInfo>) => {
    if (userInfo.value) {
      userInfo.value = { ...userInfo.value, ...updates }
      localStorage.setItem('userInfo', JSON.stringify(userInfo.value))
    }
  }

  return {
    // 状态
    userInfo,
    isLoggedIn,
    userRole,
    isAdmin,

    // 方法
    setUser,
    logout,
    restoreUser,
    updateUser,
  }
})
