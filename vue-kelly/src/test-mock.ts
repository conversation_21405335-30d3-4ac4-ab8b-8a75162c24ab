/*
 * @Author: <NAME_EMAIL>
 * @Date: 2025-08-03 23:25:00
 * @LastEditors: <NAME_EMAIL>
 * @LastEditTime: 2025-08-03 23:25:00
 * @FilePath: /vue-kelly/src/test-mock.ts
 * @Description: 测试 Mock API
 */

import { login, getTestAccounts } from '@/api/auth'

// 测试登录 API
const testLogin = async () => {
  console.log('=== 测试登录 API ===')
  
  try {
    // 测试正确的登录
    console.log('测试正确登录...')
    const result1 = await login({
      username: 'admin',
      password: 'admin123'
    })
    console.log('登录结果:', result1)
    
    // 测试错误的登录
    console.log('测试错误登录...')
    const result2 = await login({
      username: 'admin',
      password: 'wrong_password'
    })
    console.log('错误登录结果:', result2)
    
  } catch (error) {
    console.error('登录测试失败:', error)
  }
}

// 测试获取测试账号 API
const testGetAccounts = async () => {
  console.log('=== 测试获取测试账号 API ===')
  
  try {
    const accounts = await getTestAccounts()
    console.log('测试账号列表:', accounts)
  } catch (error) {
    console.error('获取测试账号失败:', error)
  }
}

// 运行所有测试
export const runMockTests = async () => {
  console.log('开始测试 Mock API...')
  
  await testLogin()
  await testGetAccounts()
  
  console.log('Mock API 测试完成!')
}

// 如果直接运行此文件，执行测试
if (import.meta.hot) {
  runMockTests()
}
