<!--
 * @Author: <NAME_EMAIL>
 * @Date: 2025-08-03 22:04:53
 * @LastEditors: <NAME_EMAIL>
 * @LastEditTime: 2025-08-03 22:46:49
 * @FilePath: /vue-kelly/src/views/about/index.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <div>
    About
    <div class="wrapper">
      <HelloWorld msg="You did it!" />

      <!-- UnoCSS 测试样式 -->
      <div class="card mb-4">
        <div class="card-header">
          <h2 class="text-xl font-bold text-primary-800">UnoCSS 已成功集成！</h2>
        </div>
        <div class="card-body">
          <p class="mb-4">这个区域使用了 UnoCSS 的原子化 CSS 类和自定义快捷方式</p>
          <div class="flex-between mb-4">
            <button class="btn-primary">主要按钮</button>
            <button class="btn-secondary">次要按钮</button>
            <button class="btn-success">成功按钮</button>
            <button class="btn-danger">危险按钮</button>
          </div>
          <div class="grid grid-cols-2 gap-4">
            <div class="p-3 bg-gray-100 rounded">
              <h3 class="font-semibold mb-2">原子化 CSS</h3>
              <p class="text-sm text-gray-600">使用 Tailwind CSS 兼容的原子化类</p>
            </div>
            <div class="p-3 bg-gray-100 rounded">
              <h3 class="font-semibold mb-2">自定义快捷方式</h3>
              <p class="text-sm text-gray-600">预定义的组合样式类</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
