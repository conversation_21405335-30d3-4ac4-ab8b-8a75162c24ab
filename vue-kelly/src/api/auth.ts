/*
 * @Author: <NAME_EMAIL>
 * @Date: 2025-08-03 23:05:00
 * @LastEditors: <NAME_EMAIL>
 * @LastEditTime: 2025-08-03 23:15:00
 * @FilePath: /vue-kelly/src/api/auth.ts
 * @Description: 认证相关 API
 */

import { post, get } from '@/utils/request'
import type { UserInfo, LoginResponse } from '@/stores/user'

// 登录请求参数接口
export interface LoginParams {
  username: string
  password: string
  remember?: boolean
}

// 测试账号接口
export interface TestAccount {
  username: string
  password: string
  name: string
  role: string
}

/**
 * 登录 API
 * @param params 登录参数
 * @returns 登录响应
 */
export const login = async (params: LoginParams): Promise<LoginResponse> => {
  const response = await post<UserInfo>('/api/auth/login', params)

  return {
    success: response.success,
    data: response.data,
    message: response.message,
  }
}

/**
 * 登出 API
 * @returns 登出响应
 */
export const logout = async (): Promise<{ success: boolean; message: string }> => {
  const response = await post('/api/auth/logout')

  return {
    success: response.success,
    message: response.message,
  }
}

/**
 * 获取用户信息 API
 * @returns 用户信息响应
 */
export const getUserInfo = async (): Promise<LoginResponse> => {
  const response = await get<UserInfo>('/api/auth/userinfo')

  return {
    success: response.success,
    data: response.data,
    message: response.message,
  }
}

/**
 * 获取测试账号列表 API
 * @returns 测试账号列表
 */
export const getTestAccounts = async (): Promise<TestAccount[]> => {
  const response = await get<TestAccount[]>('/api/auth/test-accounts')

  if (response.success && response.data) {
    return response.data
  }

  // 如果 API 失败，返回默认测试账号
  return [
    { username: 'admin', password: 'admin123', name: '管理员', role: 'admin' },
    { username: 'user', password: 'user123', name: '普通用户', role: 'user' },
    { username: 'guest', password: 'guest123', name: '访客用户', role: 'guest' },
  ]
}

// 为了向后兼容，保留 mockLogin 函数
export const mockLogin = login
