/*
 * @Author: <NAME_EMAIL>
 * @Date: 2025-04-19 12:16:00
 * @LastEditors: <NAME_EMAIL>
 * @LastEditTime: 2025-08-03 22:55:49
 * @FilePath: /vue-kelly/src/router/index.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import { createRouter, createWebHistory } from 'vue-router'
import BasicLayout from '../layouts/BasicLayout.vue'
import HomeView from '@/views/home/<USER>'

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/',
      component: BasicLayout,
      children: [
        {
          path: '',
          name: 'home',
          component: HomeView,
        },
        {
          path: '/about',
          name: 'about',
          component: () => import('@/views/about/index.vue'),
        },
        {
          path: '/users',
          name: 'users',
          component: () => import('../views/UsersView/index.vue'),
        },
        // {
        //   path: '/profile',
        //   name: 'profile',
        //   component: () => import('../views/ProfileView.vue'),
        // },
      ],
    },
  ],
})

export default router
