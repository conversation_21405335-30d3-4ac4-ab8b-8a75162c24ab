/*
 * @Author: <NAME_EMAIL>
 * @Date: 2025-04-19 12:16:00
 * @LastEditors: <NAME_EMAIL>
 * @LastEditTime: 2025-08-03 22:44:22
 * @FilePath: /projects/vue-kelly/src/main.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import { createApp } from 'vue'
import { createPinia } from 'pinia'

import App from './App.vue'
import router from './router'

import Antd from 'ant-design-vue'
import 'ant-design-vue/dist/reset.css'
import 'virtual:uno.css'
import './assets/main.css'
import './assets/global.css'

const app = createApp(App)

app.use(createPinia())
app.use(router)
app.use(Antd)
app.mount('#app')
