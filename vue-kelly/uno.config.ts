import {
  defineConfig,
  presetAttributify,
  presetIcons,
  presetUno,
  presetWebFonts,
  transformerDirectives,
  transformerVariantGroup,
} from 'unocss'

export default defineConfig({
  shortcuts: [
    // 按钮样式
    [
      'btn',
      'px-4 py-2 rounded inline-block bg-blue-600 text-white cursor-pointer hover:bg-blue-700 disabled:cursor-default disabled:bg-gray-600 disabled:opacity-50 transition-colors duration-200',
    ],
    ['btn-primary', 'btn bg-blue-600 hover:bg-blue-700'],
    ['btn-secondary', 'btn bg-gray-600 hover:bg-gray-700'],
    ['btn-success', 'btn bg-green-600 hover:bg-green-700'],
    ['btn-danger', 'btn bg-red-600 hover:bg-red-700'],
    ['btn-warning', 'btn bg-yellow-600 hover:bg-yellow-700'],

    // 卡片样式
    ['card', 'bg-white rounded-lg shadow-md p-6'],
    ['card-header', 'border-b border-gray-200 pb-4 mb-4'],
    ['card-body', 'text-gray-700'],

    // 图标按钮
    [
      'icon-btn',
      'text-[0.9em] inline-block cursor-pointer select-none opacity-75 transition duration-200 ease-in-out hover:opacity-100 hover:text-blue-600 !outline-none',
    ],

    // 输入框样式
    [
      'input',
      'border border-gray-300 rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent',
    ],

    // 布局相关
    ['flex-center', 'flex items-center justify-center'],
    ['flex-between', 'flex items-center justify-between'],
  ],

  theme: {
    colors: {
      primary: {
        50: '#eff6ff',
        100: '#dbeafe',
        200: '#bfdbfe',
        300: '#93c5fd',
        400: '#60a5fa',
        500: '#3b82f6',
        600: '#2563eb',
        700: '#1d4ed8',
        800: '#1e40af',
        900: '#1e3a8a',
      },
    },
  },

  presets: [
    presetUno(),
    presetAttributify(),
    presetIcons({
      scale: 1.2,
      warn: true,
      collections: {
        // 可以添加图标集合
        carbon: () => import('@iconify-json/carbon/icons.json').then((i) => i.default),
        mdi: () => import('@iconify-json/mdi/icons.json').then((i) => i.default),
      },
    }),
    presetWebFonts({
      fonts: {
        sans: 'Inter:400,500,600,700',
        mono: 'Fira Code:400,500',
      },
    }),
  ],

  transformers: [transformerDirectives(), transformerVariantGroup()],

  safelist: [
    // 确保这些类始终被包含
    'btn',
    'card',
    'input',
  ],
})
